!function(e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).i18nextHttpBackend=e()}(function(){return function o(r,i,a){function s(t,e){if(!i[t]){if(!r[t]){var n="function"==typeof require&&require;if(!e&&n)return n(t,!0);if(f)return f(t,!0);throw(e=new Error("Cannot find module '"+t+"'")).code="MODULE_NOT_FOUND",e}n=i[t]={exports:{}},r[t][0].call(n.exports,function(e){return s(r[t][1][e]||e)},n,n.exports,o,r,i,a)}return i[t].exports}for(var f="function"==typeof require&&require,e=0;e<a.length;e++)s(a[e]);return s}({1:[function(n,o,r){!function(t){!function(){var e="function"==typeof fetch?fetch:void 0;void 0!==t&&t.fetch?e=t.fetch:"undefined"!=typeof window&&window.fetch&&(e=window.fetch),void 0!==n&&"undefined"==typeof window&&((e=e||n("cross-fetch")).default&&(e=e.default),r.default=e,o.exports=r.default)}.call(this)}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"cross-fetch":5}],2:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var s=e("./utils.js"),r=(e=e("./request.js"))&&e.__esModule?e:{default:e};function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(t,e){var n,o=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),o.push.apply(o,n)),o}function a(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?o(Object(n),!0).forEach(function(e){u(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function f(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,c(o.key),o)}}function u(e,t,n){return(t=c(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){e=((e,t)=>{if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);if("object"!=i(n=n.call(e,t||"default")))return n;throw new TypeError("@@toPrimitive must return a primitive value.")})(e,"string");return"symbol"==i(e)?e:e+""}e=function e(t){var n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},r=this,i=e;if(!(r instanceof i))throw new TypeError("Cannot call a class as a function");this.services=t,this.options=n,this.allOptions=o,this.type="backend",this.init(t,n,o)},(d=[{key:"init",value:function(e){var t=this,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};this.services=e,this.options=a(a(a({},{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",parse:function(e){return JSON.parse(e)},stringify:JSON.stringify,parsePayload:function(e,t,n){return u({},t,n||"")},parseLoadPayload:function(e,t){},request:r.default,reloadInterval:"undefined"==typeof window&&36e5,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}),this.options||{}),n),this.allOptions=o,this.services&&this.options.reloadInterval&&"object"===i(e=setInterval(function(){return t.reload()},this.options.reloadInterval))&&"function"==typeof e.unref&&e.unref()}},{key:"readMulti",value:function(e,t,n){this._readAny(e,e,t,t,n)}},{key:"read",value:function(e,t,n){this._readAny([e],e,[t],t,n)}},{key:"_readAny",value:function(t,n,o,r,i){var a=this,e=this.options.loadPath;"function"==typeof this.options.loadPath&&(e=this.options.loadPath(t,o)),(e=(0,s.makePromise)(e)).then(function(e){if(!e)return i(null,{});e=a.services.interpolator.interpolate(e,{lng:t.join("+"),ns:o.join("+")});a.loadUrl(e,i,n,r)})}},{key:"loadUrl",value:function(i,a,s,f){var u=this,e=this.options.parseLoadPayload("string"==typeof s?[s]:s,"string"==typeof f?[f]:f);this.options.request(this.options,i,e,function(e,t){if(t&&(500<=t.status&&t.status<600||!t.status))return a("failed loading "+i+"; status code: "+t.status,!0);if(t&&400<=t.status&&t.status<500)return a("failed loading "+i+"; status code: "+t.status,!1);if(!t&&e&&e.message){var n=e.message.toLowerCase();if(["failed","fetch","network","load"].find(function(e){return-1<n.indexOf(e)}))return a("failed loading "+i+": "+e.message,!0)}if(e)return a(e,!1);var o,r;try{o="string"==typeof t.data?u.options.parse(t.data,s,f):t.data}catch(e){r="failed parsing "+i+" to json"}if(r)return a(r,!1);a(null,o)})}},{key:"create",value:function(n,o,e,t,r){var i,a,s,f,u=this;this.options.addPath&&("string"==typeof n&&(n=[n]),i=this.options.parsePayload(o,e,t),a=0,s=[],f=[],n.forEach(function(e){var t=u.options.addPath,t=("function"==typeof u.options.addPath&&(t=u.options.addPath(e,o)),u.services.interpolator.interpolate(t,{lng:e,ns:o}));u.options.request(u.options,t,i,function(e,t){a+=1,s.push(e),f.push(t),a===n.length&&"function"==typeof r&&r(s,f)})}))}},{key:"reload",value:function(){var t,e,n=this,o=this.services,r=o.backendConnector,i=o.languageUtils,a=o.logger,o=r.language;o&&"cimode"===o.toLowerCase()||(t=[],(e=function(e){i.toResolveHierarchy(e).forEach(function(e){t.indexOf(e)<0&&t.push(e)})})(o),this.allOptions.preload&&this.allOptions.preload.forEach(e),t.forEach(function(o){n.allOptions.ns.forEach(function(n){r.read(o,n,"read",null,null,function(e,t){e&&a.warn("loading namespace ".concat(n," for language ").concat(o," failed"),e),!e&&t&&a.log("loaded namespace ".concat(n," for language ").concat(o),t),r.loaded("".concat(o,"|").concat(n),e,t)})})}))}}])&&f(e.prototype,d),l&&f(e,l),Object.defineProperty(e,"prototype",{writable:!1});var l,d=e;d.type="backend",n.default=d;t.exports=n.default},{"./request.js":3,"./utils.js":4}],3:[function(n,o,r){!function(S){!function(){Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var h=n("./utils.js"),e=((e,t)=>{if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=v(e)&&"function"!=typeof e)return{default:e};if((t=a(t))&&t.has(e))return t.get(e);var n,o,r={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(n in e)"default"!==n&&{}.hasOwnProperty.call(e,n)&&((o=i?Object.getOwnPropertyDescriptor(e,n):null)&&(o.get||o.set)?Object.defineProperty(r,n,o):r[n]=e[n]);return r.default=e,t&&t.set(e,r),r})(n("./getFetch.js"));function a(e){var t,n;return"function"!=typeof WeakMap?null:(t=new WeakMap,n=new WeakMap,(a=function(e){return e?n:t})(e))}function t(t,e){var n,o=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),o.push.apply(o,n)),o}function b(o){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t(Object(r),!0).forEach(function(e){var t,n;t=o,n=r[e=e],(e=(e=>(e=((e,t)=>{if("object"!=v(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);if("object"!=v(n=n.call(e,t||"default")))return n;throw new TypeError("@@toPrimitive must return a primitive value.")})(e,"string"),"symbol"==v(e)?e:e+""))(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach(function(e){Object.defineProperty(o,e,Object.getOwnPropertyDescriptor(r,e))})}return o}function v(e){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var m,g,w="function"==typeof fetch?fetch:void 0,O=(void 0!==S&&S.fetch?w=S.fetch:"undefined"!=typeof window&&window.fetch&&(w=window.fetch),(0,h.hasXMLHttpRequest)()&&(void 0!==S&&S.XMLHttpRequest?m=S.XMLHttpRequest:"undefined"!=typeof window&&window.XMLHttpRequest&&(m=window.XMLHttpRequest)),"function"==typeof ActiveXObject&&(void 0!==S&&S.ActiveXObject?g=S.ActiveXObject:"undefined"!=typeof window&&window.ActiveXObject&&(g=window.ActiveXObject)),"function"!=typeof(w=w||!e||m||g?w:e.default||e)&&(w=void 0),function(e,t){if(t&&"object"===v(t)){var n,o="";for(n in t)o+="&"+encodeURIComponent(n)+"="+encodeURIComponent(t[n]);if(!o)return e;e=e+(-1!==e.indexOf("?")?"&":"?")+o.slice(1)}return e}),j=function(e,t,n,o){function r(t){if(!t.ok)return n(t.statusText||"Error",{status:t.status});t.text().then(function(e){n(null,{status:t.status,data:e})}).catch(n)}if(o){o=o(e,t);if(o instanceof Promise)return void o.then(r).catch(n)}("function"==typeof fetch?fetch:w)(e,t).then(r).catch(n)},P=!1;r.default=function(e,t,n,o){if("function"==typeof n&&(o=n,n=void 0),o=o||function(){},w&&0!==t.indexOf("file:")){var r=e,i=t,a=n,s=o,f=(r.queryStringParams&&(i=O(i,r.queryStringParams)),b({},"function"==typeof r.customHeaders?r.customHeaders():r.customHeaders)),u=("undefined"==typeof window&&void 0!==S&&void 0!==S.process&&S.process.versions&&S.process.versions.node&&(f["User-Agent"]="i18next-http-backend (node/".concat(S.process.version,"; ").concat(S.process.platform," ").concat(S.process.arch,")")),a&&(f["Content-Type"]="application/json"),"function"==typeof r.requestOptions?r.requestOptions(a):r.requestOptions),c=b({method:a?"POST":"GET",body:a?r.stringify(a):void 0,headers:f},P?{}:u),a="function"==typeof r.alternateFetch&&1<=r.alternateFetch.length?r.alternateFetch:void 0;try{j(i,c,s,a)}catch(e){if(!u||0===Object.keys(u).length||!e.message||e.message.indexOf("not implemented")<0)return s(e);try{Object.keys(u).forEach(function(e){delete c[e]}),j(i,c,s,a),P=!0}catch(e){s(e)}}}else if((0,h.hasXMLHttpRequest)()||"function"==typeof ActiveXObject){var f=e,r=t,u=n,l=o;u&&"object"===v(u)&&(u=O("",u).slice(1)),f.queryStringParams&&(r=O(r,f.queryStringParams));try{var d=m?new m:new g("MSXML2.XMLHTTP.3.0"),p=(d.open(u?"POST":"GET",r,1),f.crossDomain||d.setRequestHeader("X-Requested-With","XMLHttpRequest"),d.withCredentials=!!f.withCredentials,u&&d.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),d.overrideMimeType&&d.overrideMimeType("application/json"),f.customHeaders);if(p="function"==typeof p?p():p)for(var y in p)d.setRequestHeader(y,p[y]);d.onreadystatechange=function(){3<d.readyState&&l(400<=d.status?d.statusText:null,{status:d.status,data:d.responseText})},d.send(u)}catch(e){console&&console.log(e)}}else o(new Error("No fetch and no xhr implementation found!"))};o.exports=r.default}.call(this)}.call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./getFetch.js":1,"./utils.js":4}],4:[function(e,t,n){function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}Object.defineProperty(n,"__esModule",{value:!0}),n.defaults=function(n){return r.call(i.call(arguments,1),function(e){if(e)for(var t in e)void 0===n[t]&&(n[t]=e[t])}),n},n.hasXMLHttpRequest=function(){return"function"==typeof XMLHttpRequest||"object"===("undefined"==typeof XMLHttpRequest?"undefined":o(XMLHttpRequest))},n.makePromise=function(e){if((e=>e&&"function"==typeof e.then)(e))return e;return Promise.resolve(e)};var n=[],r=n.forEach,i=n.slice},{}],5:[function(e,t,n){},{}]},{},[2])(2)});