!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?module.exports=o():"function"==typeof define&&define.amd?define(o):(e="undefined"!=typeof globalThis?globalThis:e||self).i18nextBrowserLanguageDetector=o()}(this,(function(){"use strict";function e(o){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(o)}function o(o){var t=function(o,t){if("object"!=e(o)||!o)return o;var n=o[Symbol.toPrimitive];if(void 0!==n){var i=n.call(o,t||"default");if("object"!=e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(o)}(o,"string");return"symbol"==e(t)?t:t+""}function t(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,o(i.key),i)}}var n=[],i=n.forEach,r=n.slice;var a=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,u=function(e,o,t,n){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{path:"/",sameSite:"strict"};t&&(i.expires=new Date,i.expires.setTime(i.expires.getTime()+60*t*1e3)),n&&(i.domain=n),document.cookie=function(e,o,t){var n=t||{};n.path=n.path||"/";var i=encodeURIComponent(o),r="".concat(e,"=").concat(i);if(n.maxAge>0){var u=n.maxAge-0;if(Number.isNaN(u))throw new Error("maxAge should be a Number");r+="; Max-Age=".concat(Math.floor(u))}if(n.domain){if(!a.test(n.domain))throw new TypeError("option domain is invalid");r+="; Domain=".concat(n.domain)}if(n.path){if(!a.test(n.path))throw new TypeError("option path is invalid");r+="; Path=".concat(n.path)}if(n.expires){if("function"!=typeof n.expires.toUTCString)throw new TypeError("option expires is invalid");r+="; Expires=".concat(n.expires.toUTCString())}if(n.httpOnly&&(r+="; HttpOnly"),n.secure&&(r+="; Secure"),n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:r+="; SameSite=Strict";break;case"lax":r+="; SameSite=Lax";break;case"strict":r+="; SameSite=Strict";break;case"none":r+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return r}(e,encodeURIComponent(o),i)},s=function(e){for(var o="".concat(e,"="),t=document.cookie.split(";"),n=0;n<t.length;n++){for(var i=t[n];" "===i.charAt(0);)i=i.substring(1,i.length);if(0===i.indexOf(o))return i.substring(o.length,i.length)}return null},c={name:"cookie",lookup:function(e){var o;if(e.lookupCookie&&"undefined"!=typeof document){var t=s(e.lookupCookie);t&&(o=t)}return o},cacheUserLanguage:function(e,o){o.lookupCookie&&"undefined"!=typeof document&&u(o.lookupCookie,e,o.cookieMinutes,o.cookieDomain,o.cookieOptions)}},l={name:"querystring",lookup:function(e){var o;if("undefined"!=typeof window){var t=window.location.search;!window.location.search&&window.location.hash&&window.location.hash.indexOf("?")>-1&&(t=window.location.hash.substring(window.location.hash.indexOf("?")));for(var n=t.substring(1).split("&"),i=0;i<n.length;i++){var r=n[i].indexOf("=");if(r>0)n[i].substring(0,r)===e.lookupQuerystring&&(o=n[i].substring(r+1))}}return o}},f=null,d=function(){if(null!==f)return f;try{f="undefined"!==window&&null!==window.localStorage;var e="i18next.translate.boo";window.localStorage.setItem(e,"foo"),window.localStorage.removeItem(e)}catch(e){f=!1}return f},g={name:"localStorage",lookup:function(e){var o;if(e.lookupLocalStorage&&d()){var t=window.localStorage.getItem(e.lookupLocalStorage);t&&(o=t)}return o},cacheUserLanguage:function(e,o){o.lookupLocalStorage&&d()&&window.localStorage.setItem(o.lookupLocalStorage,e)}},p=null,h=function(){if(null!==p)return p;try{p="undefined"!==window&&null!==window.sessionStorage;var e="i18next.translate.boo";window.sessionStorage.setItem(e,"foo"),window.sessionStorage.removeItem(e)}catch(e){p=!1}return p},m={name:"sessionStorage",lookup:function(e){var o;if(e.lookupSessionStorage&&h()){var t=window.sessionStorage.getItem(e.lookupSessionStorage);t&&(o=t)}return o},cacheUserLanguage:function(e,o){o.lookupSessionStorage&&h()&&window.sessionStorage.setItem(o.lookupSessionStorage,e)}},v={name:"navigator",lookup:function(e){var o=[];if("undefined"!=typeof navigator){if(navigator.languages)for(var t=0;t<navigator.languages.length;t++)o.push(navigator.languages[t]);navigator.userLanguage&&o.push(navigator.userLanguage),navigator.language&&o.push(navigator.language)}return o.length>0?o:void 0}},w={name:"htmlTag",lookup:function(e){var o,t=e.htmlTag||("undefined"!=typeof document?document.documentElement:null);return t&&"function"==typeof t.getAttribute&&(o=t.getAttribute("lang")),o}},y={name:"path",lookup:function(e){var o;if("undefined"!=typeof window){var t=window.location.pathname.match(/\/([a-zA-Z-]*)/g);if(t instanceof Array)if("number"==typeof e.lookupFromPathIndex){if("string"!=typeof t[e.lookupFromPathIndex])return;o=t[e.lookupFromPathIndex].replace("/","")}else o=t[0].replace("/","")}return o}},S={name:"subdomain",lookup:function(e){var o="number"==typeof e.lookupFromSubdomainIndex?e.lookupFromSubdomainIndex+1:1,t="undefined"!=typeof window&&window.location&&window.location.hostname&&window.location.hostname.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i);if(t)return t[o]}},k=!1;try{document.cookie,k=!0}catch(e){}var b=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];k||b.splice(1,1);var x=function(){return e=function e(o){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e,o){if(!(e instanceof o))throw new TypeError("Cannot call a class as a function")}(this,e),this.type="languageDetector",this.detectors={},this.init(o,t)},o=[{key:"init",value:function(e){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.services=e||{languageUtils:{}},this.options=function(e){return i.call(r.call(arguments,1),(function(o){if(o)for(var t in o)void 0===e[t]&&(e[t]=o[t])})),e}(o,this.options||{},{order:b,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:function(e){return e}}),"string"==typeof this.options.convertDetectedLanguage&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=function(e){return e.replace("-","_")}),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=t,this.addDetector(c),this.addDetector(l),this.addDetector(g),this.addDetector(m),this.addDetector(v),this.addDetector(w),this.addDetector(y),this.addDetector(S)}},{key:"addDetector",value:function(e){return this.detectors[e.name]=e,this}},{key:"detect",value:function(e){var o=this;e||(e=this.options.order);var t=[];return e.forEach((function(e){if(o.detectors[e]){var n=o.detectors[e].lookup(o.options);n&&"string"==typeof n&&(n=[n]),n&&(t=t.concat(n))}})),t=t.map((function(e){return o.options.convertDetectedLanguage(e)})),this.services.languageUtils.getBestMatchFromCodes?t:t.length>0?t[0]:null}},{key:"cacheUserLanguage",value:function(e,o){var t=this;o||(o=this.options.caches),o&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||o.forEach((function(o){t.detectors[o]&&t.detectors[o].cacheUserLanguage(e,t.options)})))}}],o&&t(e.prototype,o),n&&t(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,o,n}();return x.type="languageDetector",x}));
